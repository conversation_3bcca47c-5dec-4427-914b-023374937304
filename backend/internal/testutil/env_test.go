package testutil

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLoadEnvForTest(t *testing.T) {
	// Save original environment
	originalDBHost := os.Getenv("DB_HOST")
	defer func() {
		if originalDBHost != "" {
			os.Setenv("DB_HOST", originalDBHost)
		} else {
			os.Unsetenv("DB_HOST")
		}
	}()

	// Clear the environment variable first
	os.Unsetenv("DB_HOST")

	// Load .env file
	LoadEnvForTest(t)

	// Check if environment variables are loaded
	dbHost := os.Getenv("DB_HOST")
	assert.NotEmpty(t, dbHost, "DB_HOST should be loaded from .env file")
	
	// The .env file should contain the pooler hostname
	assert.Contains(t, dbHost, "pooler", "DB_HOST should contain 'pooler' from .env file")
}

func TestLoadEnvForTestSilent(t *testing.T) {
	// Save original environment
	originalDBHost := os.Getenv("DB_HOST")
	defer func() {
		if originalDBHost != "" {
			os.Setenv("DB_HOST", originalDBHost)
		} else {
			os.Unsetenv("DB_HOST")
		}
	}()

	// Clear the environment variable first
	os.Unsetenv("DB_HOST")

	// Load .env file silently
	LoadEnvForTestSilent(t)

	// Check if environment variables are loaded
	dbHost := os.Getenv("DB_HOST")
	assert.NotEmpty(t, dbHost, "DB_HOST should be loaded from .env file")
	
	// The .env file should contain the pooler hostname
	assert.Contains(t, dbHost, "pooler", "DB_HOST should contain 'pooler' from .env file")
}

func TestLoadEnvForTest_NoEnvFile(t *testing.T) {
	// This test verifies that the function doesn't fail when no .env file is found
	// We'll temporarily change to a directory where there's no .env file
	
	// Save current directory
	originalDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	defer os.Chdir(originalDir)

	// Change to a temporary directory
	tmpDir := t.TempDir()
	err = os.Chdir(tmpDir)
	if err != nil {
		t.Fatalf("Failed to change to temp directory: %v", err)
	}

	// This should not panic or fail, just log a warning
	LoadEnvForTest(t)
	
	// Test passes if we reach this point without panicking
	assert.True(t, true, "LoadEnvForTest should not fail when no .env file is found")
}
