package testutil

import (
	"log"
	"testing"

	"github.com/joho/godotenv"
)

// LoadEnvForTest loads .env file for tests, trying multiple paths.
// This function consolidates the .env loading logic that was duplicated
// across multiple test files. It tries to find the .env file in common
// locations relative to where tests might be run from.
//
// The function tries the following paths in order:
//   - .env (current directory)
//   - ../.env (parent directory)
//   - ../../.env (grandparent directory)
//   - ../../../.env (great-grandparent directory)
//
// If no .env file is found, it logs a warning but does not fail the test,
// allowing tests to fall back to system environment variables.
//
// This function replaces the duplicate .env loading code that was previously
// found in:
//   - backend/internal/database/postgres_test.go
//   - backend/internal/repository/postgres_todo_test.go
//   - backend/test/testutil/database.go
//
// Usage:
//   func TestSomething(t *testing.T) {
//       testutil.LoadEnvForTest(t)
//       // ... rest of test
//   }
func LoadEnvForTest(t *testing.T) {
	// Try multiple paths to find the .env file
	envPaths := []string{".env", "../.env", "../../.env", "../../../.env"}
	var envLoaded bool
	
	for _, path := range envPaths {
		if err := godotenv.Load(path); err == nil {
			envLoaded = true
			t.Logf("Loaded .env file from: %s", path)
			break
		}
	}
	
	if !envLoaded {
		log.Printf("Warning: Could not load .env file for tests from any of the paths: %v", envPaths)
		t.Logf("Warning: Could not load .env file for tests from any of the paths: %v", envPaths)
	}
}

// LoadEnvForTestSilent loads .env file for tests without logging success.
// This is useful for tests that don't want verbose logging about .env loading.
// It still logs warnings if no .env file is found.
func LoadEnvForTestSilent(t *testing.T) {
	// Try multiple paths to find the .env file
	envPaths := []string{".env", "../.env", "../../.env", "../../../.env"}
	var envLoaded bool
	
	for _, path := range envPaths {
		if err := godotenv.Load(path); err == nil {
			envLoaded = true
			break
		}
	}
	
	if !envLoaded {
		log.Printf("Warning: Could not load .env file for tests from any of the paths: %v", envPaths)
		t.Logf("Warning: Could not load .env file for tests from any of the paths: %v", envPaths)
	}
}
