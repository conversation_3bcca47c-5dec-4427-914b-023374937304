package handlers

import (
	"net/http"

	"github.com/labstack/echo/v4"

	"backend/internal/errors"
	"backend/internal/models"
	"backend/internal/service"
)

// Response structs
type GetAllTodosResponse struct {
	Todos []*models.Todo `json:"todos"`
	Count int            `json:"count"`
}

type MessageResponse struct {
	Message string `json:"message"`
}

// TodoHandler handles HTTP requests for todo operations
type TodoHandler struct {
	service service.TodoService
}

// NewTodoHandler creates a new todo handler
func NewTodoHandler(service service.TodoService) *TodoHandler {
	return &TodoHandler{
		service: service,
	}
}

// CreateTodo handles POST /todos
func (h *TodoHandler) CreateTodo(c echo.Context) error {
	var req models.CreateTodoRequest
	if err := c.Bind(&req); err != nil {
		return h.handleError(c, errors.NewBadRequestError("invalid request body"))
	}

	todo, err := h.service.CreateTodo(c.Request().Context(), &req)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusCreated, todo)
}

// GetTodo handles GET /todos/{id}
func (h *TodoHandler) GetTodo(c echo.Context) error {
	id := c.Param("id")
	if id == "" {
		return h.handleError(c, errors.NewBadRequestError("todo ID is required"))
	}

	todo, err := h.service.GetTodoByID(c.Request().Context(), id)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, todo)
}

// GetAllTodos handles GET /todos
func (h *TodoHandler) GetAllTodos(c echo.Context) error {
	todos, err := h.service.GetAllTodos(c.Request().Context())
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, GetAllTodosResponse{
		Todos: todos,
		Count: len(todos),
	})
}

// UpdateTodo handles PUT /todos/{id}
func (h *TodoHandler) UpdateTodo(c echo.Context) error {
	id := c.Param("id")
	if id == "" {
		return h.handleError(c, errors.NewBadRequestError("todo ID is required"))
	}

	var req models.UpdateTodoRequest
	if err := c.Bind(&req); err != nil {
		return h.handleError(c, errors.NewBadRequestError("invalid request body"))
	}

	todo, err := h.service.UpdateTodo(c.Request().Context(), id, &req)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, todo)
}

// DeleteTodo handles DELETE /todos/{id}
func (h *TodoHandler) DeleteTodo(c echo.Context) error {
	id := c.Param("id")
	if id == "" {
		return h.handleError(c, errors.NewBadRequestError("todo ID is required"))
	}

	err := h.service.DeleteTodo(c.Request().Context(), id)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, MessageResponse{
		Message: "todo deleted successfully",
	})
}

// BatchDeleteTodos handles POST /todos/batch-delete
func (h *TodoHandler) BatchDeleteTodos(c echo.Context) error {
	var req models.BatchDeleteTodosRequest
	if err := c.Bind(&req); err != nil {
		return h.handleError(c, errors.NewBadRequestError("invalid request body"))
	}

	deletedCount, err := h.service.BatchDeleteTodos(c.Request().Context(), &req)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message":       "todos deleted successfully",
		"deleted_count": deletedCount,
	})
}

// handleError handles application errors and returns appropriate HTTP responses
func (h *TodoHandler) handleError(c echo.Context, err error) error {
	if appErr, ok := err.(*errors.AppError); ok {
		// Log the underlying error if it's an internal server error
		if appErr.Code == http.StatusInternalServerError && appErr.Err != nil {
			c.Logger().Error(appErr)
		}
		return c.JSON(appErr.Code, map[string]interface{}{
			"error":   true,
			"message": appErr.Message,
		})
	}

	// Log unexpected errors
	c.Logger().Error(err)

	// Handle unexpected errors
	return c.JSON(http.StatusInternalServerError, map[string]interface{}{
		"error":   true,
		"message": "internal server error",
	})
}
